#!/usr/bin/env python3
"""
Simple web server to provide real FlashScore data to HTML interface
Serves real data from the modernized parser instead of test data
"""

import json
import logging
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import WhitelistMode

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FlashScoreAPIHandler(BaseHTTPRequestHandler):
    """HTTP request handler for FlashScore API endpoints"""
    
    def __init__(self, *args, **kwargs):
        self.parser = FlashScoreParser()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            # Enable CORS for browser requests
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            
            if path == '/api/matches':
                self.handle_matches_request(query_params)
            elif path == '/api/leagues':
                self.handle_leagues_request(query_params)
            elif path == '/api/whitelist-stats':
                self.handle_whitelist_stats_request(query_params)
            elif path == '/api/filtering-summary':
                self.handle_filtering_summary_request(query_params)
            elif path == '/':
                self.serve_html_page()
            else:
                self.send_error(404, "Endpoint not found")
                
        except Exception as e:
            logger.error(f"Error handling request: {e}")
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def handle_matches_request(self, query_params):
        """Handle /api/matches requests"""
        try:
            # Get parameters
            sport_id = int(query_params.get('sport', ['1'])[0])
            day_offset = int(query_params.get('day', ['0'])[0])
            use_whitelist = query_params.get('whitelist', ['true'])[0].lower() == 'true'
            whitelist_mode = query_params.get('mode', ['country_id'])[0]
            
            logger.info(f"Fetching matches: sport={sport_id}, day={day_offset}, whitelist={use_whitelist}, mode={whitelist_mode}")
            
            # Configure whitelist
            if whitelist_mode == 'country_id':
                self.parser.update_whitelist_mode(WhitelistMode.COUNTRY_ID)
            elif whitelist_mode == 'league_name':
                self.parser.update_whitelist_mode(WhitelistMode.LEAGUE_NAME)
            elif whitelist_mode == 'combined':
                self.parser.update_whitelist_mode(WhitelistMode.COMBINED)
            
            self.parser.enable_whitelist(use_whitelist)
            
            # Get matches
            if day_offset == 0:
                matches = self.parser.get_today_matches(sport_id, apply_whitelist=use_whitelist)
            else:
                matches = self.parser.get_matches_for_date_range(sport_id, [day_offset], apply_whitelist=use_whitelist)
            
            # Convert to JSON-serializable format
            matches_data = []
            for match in matches:
                match_data = {
                    "event_id": match.event_id,
                    "start_time": match.start_time.isoformat(),
                    "sport_id": match.sport_id,
                    "league_name": match.league_name,
                    "country_id": match.country_id,
                    "country_name": match.country_name,
                    "home_team": {
                        "name": match.home_team.name,
                        "short_name": match.home_team.short_name,
                        "image_url": match.home_team.image_url
                    },
                    "away_team": {
                        "name": match.away_team.name,
                        "short_name": match.away_team.short_name,
                        "image_url": match.away_team.image_url
                    },
                    "status": match.status,
                    "home_score": match.home_score,
                    "away_score": match.away_score,
                    "current_result": match.current_result,
                    "match_url": match.match_url
                }
                matches_data.append(match_data)
            
            # Create response
            response_data = {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(matches_data),
                "sport_id": sport_id,
                "day_offset": day_offset,
                "whitelist_enabled": use_whitelist,
                "whitelist_mode": whitelist_mode,
                "matches": matches_data
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            
            response_json = json.dumps(response_data, ensure_ascii=False, indent=2)
            self.wfile.write(response_json.encode('utf-8'))
            
            logger.info(f"Sent {len(matches_data)} matches")
            
        except Exception as e:
            logger.error(f"Error in matches request: {e}")
            error_response = {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_response(500)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            
            self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))
    
    def handle_leagues_request(self, query_params):
        """Handle /api/leagues requests"""
        try:
            sport_id = int(query_params.get('sport', ['1'])[0])
            
            leagues = self.parser.get_leagues_for_today(sport_id)
            
            response_data = {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "sport_id": sport_id,
                "total_leagues": len(leagues),
                "leagues": leagues
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            
            response_json = json.dumps(response_data, ensure_ascii=False, indent=2)
            self.wfile.write(response_json.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"Error in leagues request: {e}")
            self.send_error(500, str(e))
    
    def handle_whitelist_stats_request(self, query_params):
        """Handle /api/whitelist-stats requests"""
        try:
            sport_id = int(query_params.get('sport', ['1'])[0])
            
            stats = self.parser.get_whitelist_stats(sport_id)
            
            response_data = {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "stats": stats
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            
            response_json = json.dumps(response_data, ensure_ascii=False, indent=2)
            self.wfile.write(response_json.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"Error in whitelist stats request: {e}")
            self.send_error(500, str(e))
    
    def handle_filtering_summary_request(self, query_params):
        """Handle /api/filtering-summary requests"""
        try:
            sport_id = int(query_params.get('sport', ['1'])[0])
            day_offset = int(query_params.get('day', ['0'])[0])
            
            summary = self.parser.get_filtered_leagues_summary(sport_id, day_offset)
            
            response_data = {
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "summary": summary
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            
            response_json = json.dumps(response_data, ensure_ascii=False, indent=2)
            self.wfile.write(response_json.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"Error in filtering summary request: {e}")
            self.send_error(500, str(e))
    
    def serve_html_page(self):
        """Serve the HTML test interface"""
        try:
            with open('real_data_interface.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            
            self.wfile.write(html_content.encode('utf-8'))
            
        except FileNotFoundError:
            self.send_error(404, "HTML interface not found")
        except Exception as e:
            logger.error(f"Error serving HTML: {e}")
            self.send_error(500, str(e))
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")


def start_server(port=8000):
    """Start the web server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, FlashScoreAPIHandler)
    
    print(f"🚀 FlashScore Web Server starting on http://localhost:{port}")
    print(f"📊 Real data interface: http://localhost:{port}/")
    print(f"🔗 API endpoints:")
    print(f"   - /api/matches?sport=1&day=0&whitelist=true&mode=country_id")
    print(f"   - /api/leagues?sport=1")
    print(f"   - /api/whitelist-stats?sport=1")
    print(f"   - /api/filtering-summary?sport=1&day=0")
    print(f"⏹️  Press Ctrl+C to stop")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n🛑 Server stopped")
        httpd.server_close()


if __name__ == "__main__":
    start_server()
