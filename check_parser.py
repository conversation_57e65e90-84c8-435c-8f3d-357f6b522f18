#!/usr/bin/env python3
"""
Простая проверка парсера - что именно он парсит и выводит
"""

from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import WhitelistMode
from whitelist import sp_good_ligs

def main():
    print("🔍 ПРОВЕРКА ПАРСЕРА - ЧТО ОН ПАРСИТ")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Получаем данные без фильтрации
    print("📡 Подключаемся к FlashScore API...")
    parser.enable_whitelist(False)
    all_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=False)
    
    print(f"✅ Получено {len(all_matches)} матчей")
    
    # Показываем первые 5 матчей
    print(f"\n📋 ПЕРВЫЕ 5 МАТЧЕЙ:")
    for i, match in enumerate(all_matches[:5]):
        print(f"\n{i+1}. {match.home_team.name} vs {match.away_team.name}")
        print(f"   🏆 Лига: {match.league_name}")
        print(f"   🌍 Country ID: {match.country_id}")
        print(f"   🕐 Время: {match.start_time.strftime('%H:%M')}")
        print(f"   📊 Статус: {match.status}")
        if match.current_result:
            print(f"   ⚽ Счет: {match.current_result}")
        print(f"   🔗 URL: {match.match_url}")
    
    # Анализируем лиги
    leagues = {}
    for match in all_matches:
        if match.league_name not in leagues:
            leagues[match.league_name] = []
        leagues[match.league_name].append(match)
    
    print(f"\n📊 СТАТИСТИКА ПО ЛИГАМ:")
    print(f"Всего уникальных лиг: {len(leagues)}")
    
    # Топ-10 лиг по количеству матчей
    sorted_leagues = sorted(leagues.items(), key=lambda x: len(x[1]), reverse=True)
    print(f"\nТоп-10 лиг по количеству матчей:")
    for i, (league, matches) in enumerate(sorted_leagues[:10]):
        print(f"  {i+1:2d}. {league}: {len(matches)} матчей")
    
    # Анализируем страны
    countries = {}
    for match in all_matches:
        if match.country_id not in countries:
            countries[match.country_id] = []
        countries[match.country_id].append(match)
    
    print(f"\n🌍 СТАТИСТИКА ПО СТРАНАМ:")
    print(f"Всего уникальных стран: {len(countries)}")
    print(f"Country IDs: {sorted(countries.keys())}")
    
    # Проверяем ваш whitelist
    print(f"\n🎯 АНАЛИЗ ВАШЕГО WHITELIST:")
    print(f"Размер whitelist: {len(sp_good_ligs)} ID")
    print(f"Первые 10 ID: {sp_good_ligs[:10]}")
    
    # Какие country_id есть в whitelist
    whitelist_countries = set(countries.keys()).intersection(set(sp_good_ligs))
    print(f"Country ID из whitelist: {sorted(whitelist_countries)}")
    
    # Применяем ваш whitelist
    print(f"\n🔧 ПРИМЕНЯЕМ ВАШ WHITELIST:")
    parser.enable_whitelist(True)
    parser.update_whitelist_mode(WhitelistMode.ORIGINAL_WHITELIST)
    filtered_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=True)
    
    print(f"После фильтрации: {len(filtered_matches)} матчей")
    
    if filtered_matches:
        print(f"\n🏆 ОТФИЛЬТРОВАННЫЕ МАТЧИ:")
        for i, match in enumerate(filtered_matches):
            print(f"\n{i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"   🏆 Лига: {match.league_name}")
            print(f"   🌍 Country ID: {match.country_id}")
            print(f"   🕐 Время: {match.start_time.strftime('%H:%M')}")
            print(f"   📊 Статус: {match.status}")
            if match.current_result:
                print(f"   ⚽ Счет: {match.current_result}")
    else:
        print("❌ Нет матчей после применения whitelist")
    
    # Сохраняем результаты
    if filtered_matches:
        parser.save_matches_to_legacy_format(filtered_matches, "check_parser_results.json")
        print(f"\n💾 Результаты сохранены в: check_parser_results.json")
    
    # Итоговая статистика
    print(f"\n📈 ИТОГОВАЯ СТАТИСТИКА:")
    print(f"Всего матчей: {len(all_matches)}")
    print(f"После whitelist: {len(filtered_matches)}")
    if len(all_matches) > 0:
        filter_rate = ((len(all_matches) - len(filtered_matches)) / len(all_matches)) * 100
        print(f"Эффективность фильтрации: {filter_rate:.1f}%")
    
    print(f"\n✅ ПРОВЕРКА ЗАВЕРШЕНА!")

if __name__ == "__main__":
    main()
