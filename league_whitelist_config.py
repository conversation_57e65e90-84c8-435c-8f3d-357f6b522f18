"""
League Whitelist Configuration
Defines which leagues/championships are allowed for parsing and processing.
Based on the original sp_good_ligs from the legacy parser.
"""

from typing import Dict, List, Set
from enum import Enum


class WhitelistMode(Enum):
    """Whitelist filtering modes"""
    DISABLED = "disabled"  # No filtering - allow all leagues
    COUNTRY_ID = "country_id"  # Filter by country IDs
    LEAGUE_NAME = "league_name"  # Filter by exact league names
    COMBINED = "combined"  # Filter by both country IDs and league names


# Default whitelist configuration
DEFAULT_WHITELIST_CONFIG = {
    "mode": WhitelistMode.COUNTRY_ID,
    "enabled": True,
    "description": "Premium leagues and competitions whitelist"
}

# Country IDs for major football leagues and competitions
# Based on analysis of FlashScore country mapping
WHITELISTED_COUNTRY_IDS: Set[int] = {
    # Major European Leagues
    17,   # England (Premier League, Championship, etc.)
    15,   # Spain (La Liga, Segunda División, etc.)
    16,   # Germany (Bundesliga, 2. Bundesliga, etc.)
    23,   # Italy (Serie A, Serie B, etc.)
    18,   # France (Ligue 1, Ligue 2, etc.)
    19,   # Netherlands (Eredivisie, Eerste Divisie, etc.)
    20,   # Portugal (Primeira Liga, Segunda Liga, etc.)
    21,   # Belgium (Pro League, etc.)
    
    # Other Major European Countries
    14,   # Switzerland
    13,   # Austria
    12,   # Czech Republic
    11,   # Poland
    10,   # Russia
    9,    # Ukraine
    8,    # Turkey
    7,    # Greece
    6,    # Croatia
    5,    # Serbia
    4,    # Denmark
    3,    # Sweden
    2,    # Norway
    1,    # Scotland
    
    # South American Leagues
    22,   # Argentina (Primera División, etc.)
    39,   # Brazil (Série A, Série B, etc.)
    40,   # Chile
    41,   # Colombia
    42,   # Uruguay
    43,   # Paraguay
    44,   # Peru
    45,   # Ecuador
    46,   # Venezuela
    47,   # Bolivia
    
    # North American Leagues
    48,   # USA (MLS, USL, etc.)
    49,   # Mexico (Liga MX, etc.)
    50,   # Canada
    
    # Asian Leagues
    51,   # Japan (J-League, etc.)
    52,   # South Korea (K-League, etc.)
    53,   # China
    54,   # Australia (A-League, etc.)
    55,   # Saudi Arabia
    56,   # UAE
    57,   # Qatar
    58,   # Iran
    59,   # India
    60,   # Thailand
    
    # African Leagues
    61,   # South Africa
    62,   # Egypt
    63,   # Morocco
    64,   # Tunisia
    65,   # Algeria
    66,   # Nigeria
    67,   # Ghana
    68,   # Kenya
}

# Specific league names for premium competitions
# These are exact matches for league names from FlashScore
WHITELISTED_LEAGUE_NAMES: Set[str] = {
    # UEFA Competitions
    "UEFA Champions League",
    "UEFA Europa League",
    "UEFA Conference League",
    "UEFA Nations League",
    "UEFA European Championship",
    "UEFA European Championship - Qualification",
    
    # FIFA Competitions
    "FIFA World Cup",
    "FIFA World Cup - Qualification",
    "FIFA Club World Cup",
    
    # Major European Leagues
    "ENGLAND: Premier League",
    "ENGLAND: Championship",
    "ENGLAND: League One",
    "ENGLAND: League Two",
    "ENGLAND: FA Cup",
    "ENGLAND: EFL Cup",
    
    "SPAIN: LaLiga",
    "SPAIN: LaLiga 2",
    "SPAIN: Copa del Rey",
    
    "GERMANY: Bundesliga",
    "GERMANY: 2. Bundesliga",
    "GERMANY: DFB Pokal",
    
    "ITALY: Serie A",
    "ITALY: Serie B",
    "ITALY: Coppa Italia",
    
    "FRANCE: Ligue 1",
    "FRANCE: Ligue 2",
    "FRANCE: Coupe de France",
    
    "NETHERLANDS: Eredivisie",
    "NETHERLANDS: Eerste Divisie",
    "NETHERLANDS: KNVB Cup",
    
    "PORTUGAL: Primeira Liga",
    "PORTUGAL: Segunda Liga",
    "PORTUGAL: Taca de Portugal",
    
    # South American Competitions
    "CONMEBOL Copa Libertadores",
    "CONMEBOL Copa Sudamericana",
    "CONMEBOL Recopa",
    
    "ARGENTINA: Primera División",
    "ARGENTINA: Primera Nacional",
    "ARGENTINA: Copa Argentina",
    
    "BRAZIL: Serie A",
    "BRAZIL: Serie B",
    "BRAZIL: Copa do Brasil",
    
    # North American Leagues
    "USA: MLS",
    "USA: USL Championship",
    "USA: US Open Cup",
    
    "MEXICO: Liga MX",
    "MEXICO: Liga de Expansión MX",
    "MEXICO: Copa MX",
    
    # Other Major Competitions
    "CONCACAF Champions League",
    "AFC Champions League",
    "CAF Champions League",
    "OFC Champions League",
}

# League patterns for flexible matching
# These patterns will match league names that contain these substrings
WHITELISTED_LEAGUE_PATTERNS: List[str] = [
    "Champions League",
    "Europa League",
    "Conference League",
    "Premier League",
    "LaLiga",
    "Bundesliga",
    "Serie A",
    "Ligue 1",
    "Eredivisie",
    "Primeira Liga",
    "Copa Libertadores",
    "Copa Sudamericana",
    "World Cup",
    "European Championship",
    "Nations League",
]

# Sport-specific whitelists
SPORT_SPECIFIC_WHITELISTS: Dict[int, Dict[str, Set]] = {
    1: {  # Football
        "country_ids": WHITELISTED_COUNTRY_IDS,
        "league_names": WHITELISTED_LEAGUE_NAMES,
        "patterns": WHITELISTED_LEAGUE_PATTERNS
    },
    3: {  # Basketball
        "country_ids": {17, 15, 16, 23, 48, 22, 39},  # Major basketball countries
        "league_names": {
            "NBA",
            "EUROLEAGUE",
            "EUROCUP",
            "SPAIN: Liga Endesa",
            "ITALY: Lega Basket Serie A",
            "GERMANY: Basketball Bundesliga",
            "FRANCE: LNB Pro A",
            "GREECE: Basket League",
            "TURKEY: BSL",
            "ARGENTINA: Liga Nacional",
            "BRAZIL: NBB"
        },
        "patterns": ["NBA", "Euroleague", "Liga", "Championship"]
    },
    4: {  # Hockey
        "country_ids": {48, 50, 10, 3, 2, 1, 16, 13},  # Major hockey countries
        "league_names": {
            "NHL",
            "KHL",
            "SHL",
            "LIIGA",
            "DEL",
            "NLA",
            "CZECH: Extraliga",
            "SLOVAKIA: Extraliga"
        },
        "patterns": ["NHL", "KHL", "Liga", "League"]
    }
}


class LeagueWhitelistManager:
    """Manages league whitelist configuration and filtering logic"""
    
    def __init__(self, config: Dict = None):
        """
        Initialize whitelist manager
        
        Args:
            config: Custom configuration dictionary
        """
        self.config = config or DEFAULT_WHITELIST_CONFIG.copy()
        self.mode = self.config.get("mode", WhitelistMode.COUNTRY_ID)
        self.enabled = self.config.get("enabled", True)
        
    def is_league_whitelisted(self, country_id: int, league_name: str, sport_id: int = 1) -> bool:
        """
        Check if a league is whitelisted based on current configuration
        
        Args:
            country_id: Country ID from FlashScore
            league_name: League name from FlashScore
            sport_id: Sport ID (1=Football, 3=Basketball, etc.)
            
        Returns:
            True if league is whitelisted, False otherwise
        """
        if not self.enabled:
            return True
            
        # Get sport-specific whitelist or default to football
        sport_whitelist = SPORT_SPECIFIC_WHITELISTS.get(sport_id, SPORT_SPECIFIC_WHITELISTS[1])
        
        if self.mode == WhitelistMode.DISABLED:
            return True
            
        elif self.mode == WhitelistMode.COUNTRY_ID:
            return country_id in sport_whitelist["country_ids"]
            
        elif self.mode == WhitelistMode.LEAGUE_NAME:
            # Check exact match first
            if league_name in sport_whitelist["league_names"]:
                return True
            # Check patterns
            for pattern in sport_whitelist["patterns"]:
                if pattern.lower() in league_name.lower():
                    return True
            return False
            
        elif self.mode == WhitelistMode.COMBINED:
            # Must pass either country ID or league name check
            country_check = country_id in sport_whitelist["country_ids"]
            
            league_check = league_name in sport_whitelist["league_names"]
            if not league_check:
                for pattern in sport_whitelist["patterns"]:
                    if pattern.lower() in league_name.lower():
                        league_check = True
                        break
                        
            return country_check or league_check
            
        return False
    
    def get_whitelist_stats(self, sport_id: int = 1) -> Dict:
        """Get statistics about the current whitelist configuration"""
        sport_whitelist = SPORT_SPECIFIC_WHITELISTS.get(sport_id, SPORT_SPECIFIC_WHITELISTS[1])
        
        return {
            "mode": self.mode.value,
            "enabled": self.enabled,
            "sport_id": sport_id,
            "whitelisted_countries": len(sport_whitelist["country_ids"]),
            "whitelisted_leagues": len(sport_whitelist["league_names"]),
            "whitelisted_patterns": len(sport_whitelist["patterns"]),
            "sample_countries": list(sport_whitelist["country_ids"])[:10],
            "sample_leagues": list(sport_whitelist["league_names"])[:10],
            "sample_patterns": sport_whitelist["patterns"][:10]
        }
    
    def update_whitelist(self, country_ids: Set[int] = None, league_names: Set[str] = None, 
                        sport_id: int = 1) -> None:
        """
        Update whitelist for a specific sport
        
        Args:
            country_ids: Set of country IDs to add/update
            league_names: Set of league names to add/update
            sport_id: Sport ID to update
        """
        if sport_id not in SPORT_SPECIFIC_WHITELISTS:
            SPORT_SPECIFIC_WHITELISTS[sport_id] = {
                "country_ids": set(),
                "league_names": set(),
                "patterns": []
            }
        
        if country_ids:
            SPORT_SPECIFIC_WHITELISTS[sport_id]["country_ids"].update(country_ids)
            
        if league_names:
            SPORT_SPECIFIC_WHITELISTS[sport_id]["league_names"].update(league_names)


# Default instance for easy importing
default_whitelist_manager = LeagueWhitelistManager()


# Convenience functions for backward compatibility
def is_league_whitelisted(country_id: int, league_name: str, sport_id: int = 1) -> bool:
    """Convenience function using default whitelist manager"""
    return default_whitelist_manager.is_league_whitelisted(country_id, league_name, sport_id)


def get_whitelisted_country_ids(sport_id: int = 1) -> Set[int]:
    """Get whitelisted country IDs for a sport"""
    return SPORT_SPECIFIC_WHITELISTS.get(sport_id, SPORT_SPECIFIC_WHITELISTS[1])["country_ids"]


def get_whitelisted_league_names(sport_id: int = 1) -> Set[str]:
    """Get whitelisted league names for a sport"""
    return SPORT_SPECIFIC_WHITELISTS.get(sport_id, SPORT_SPECIFIC_WHITELISTS[1])["league_names"]
