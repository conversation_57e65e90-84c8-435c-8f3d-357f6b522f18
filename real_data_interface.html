<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlashScore Parser - Реальные Данные</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.2em;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
        }
        
        .control-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        select, button {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        select:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-bar {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
        }
        
        .status-indicator.loading {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }
        
        .status-indicator.error {
            background: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .matches-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .match-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .match-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .teams {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        
        .vs {
            color: #667eea;
            margin: 0 10px;
        }
        
        .match-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-finished {
            background: #d4edda;
            color: #155724;
        }
        
        .status-live {
            background: #f8d7da;
            color: #721c24;
            animation: pulse 2s infinite;
        }
        
        .status-scheduled {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .match-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            color: #666;
        }
        
        .detail-icon {
            width: 16px;
            height: 16px;
            opacity: 0.7;
        }
        
        .league-name {
            color: #667eea;
            font-weight: bold;
        }
        
        .score {
            font-size: 1.5em;
            font-weight: bold;
            color: #28a745;
        }
        
        .loading {
            text-align: center;
            padding: 60px;
            color: #666;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        
        .no-matches {
            text-align: center;
            padding: 60px;
            color: #666;
        }
        
        .no-matches-icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }
        
        .export-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .export-btn {
            background: #28a745;
            padding: 8px 16px;
            font-size: 0.9em;
        }
        
        .export-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 FlashScore Parser</h1>
            <p>Реальные данные с фильтрацией по лигам</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="sport-select">Спорт:</label>
                <select id="sport-select">
                    <option value="1">⚽ Футбол</option>
                    <option value="3">🏀 Баскетбол</option>
                    <option value="4">🏒 Хоккей</option>
                    <option value="2">🎾 Теннис</option>
                    <option value="6">⚾ Бейсбол</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="day-select">День:</label>
                <select id="day-select">
                    <option value="-1">Вчера</option>
                    <option value="0" selected>Сегодня</option>
                    <option value="1">Завтра</option>
                    <option value="2">Послезавтра</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="whitelist-select">Фильтрация:</label>
                <select id="whitelist-select">
                    <option value="true">✅ Включена</option>
                    <option value="false">❌ Отключена</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="mode-select">Режим фильтрации:</label>
                <select id="mode-select">
                    <option value="country_id">По странам</option>
                    <option value="league_name">По лигам</option>
                    <option value="combined">Комбинированный</option>
                    <option value="original_whitelist">🎯 Оригинальный whitelist</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>&nbsp;</label>
                <button onclick="fetchRealData()" id="fetch-btn">🔄 Загрузить данные</button>
            </div>
        </div>
        
        <div class="status-bar" id="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="status-indicator"></div>
                <span id="status-text">Готов к загрузке</span>
            </div>
            <div class="status-item">
                <span id="last-update">Данные не загружены</span>
            </div>
        </div>
        
        <div class="stats" id="stats-container" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="total-matches">0</div>
                <div class="stat-label">Всего матчей</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="live-matches">0</div>
                <div class="stat-label">Идут сейчас</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="finished-matches">0</div>
                <div class="stat-label">Завершены</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="scheduled-matches">0</div>
                <div class="stat-label">Запланированы</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="unique-leagues">0</div>
                <div class="stat-label">Лиг</div>
            </div>
        </div>
        
        <div class="export-buttons" id="export-buttons" style="display: none;">
            <button class="export-btn" onclick="exportJSON()">📄 Экспорт JSON</button>
            <button class="export-btn" onclick="exportLegacy()">📋 Экспорт Legacy</button>
            <button class="export-btn" onclick="showWhitelistStats()">📊 Статистика фильтрации</button>
        </div>
        
        <div class="matches-container" id="matches-container">
            <div class="loading" id="welcome-message">
                <h3>🚀 Добро пожаловать в FlashScore Parser</h3>
                <p>Выберите параметры и нажмите "Загрузить данные" для получения реальных данных с FlashScore API</p>
                <p><strong>Рекомендация:</strong> Начните с Футбол + Сегодня + Фильтрация включена</p>
            </div>
        </div>
    </div>

    <script>
        let currentData = null;
        let currentStats = null;
        
        function updateStatus(text, type = 'ready') {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            indicator.className = `status-indicator ${type}`;
            statusText.textContent = text;
            
            if (type === 'success') {
                document.getElementById('last-update').textContent = 
                    `Обновлено: ${new Date().toLocaleString('ru-RU')}`;
            }
        }
        
        function showLoading() {
            updateStatus('Загрузка данных...', 'loading');
            document.getElementById('fetch-btn').disabled = true;
            document.getElementById('matches-container').innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <h3>Загрузка реальных данных...</h3>
                    <p>Подключение к FlashScore API и получение актуальных матчей</p>
                </div>
            `;
        }
        
        function showError(message) {
            updateStatus('Ошибка загрузки', 'error');
            document.getElementById('fetch-btn').disabled = false;
            document.getElementById('matches-container').innerHTML = `
                <div class="error">
                    <h3>❌ Ошибка загрузки данных</h3>
                    <p>${message}</p>
                    <p>Проверьте, что веб-сервер запущен: <code>python3 web_server.py</code></p>
                </div>
            `;
        }
        
        function displayMatches(data) {
            if (!data.success) {
                showError(data.error || 'Неизвестная ошибка');
                return;
            }
            
            currentData = data;
            updateStatus(`Загружено ${data.total_matches} матчей`, 'success');
            document.getElementById('fetch-btn').disabled = false;
            
            // Update statistics
            updateStatistics(data.matches);
            
            if (data.matches.length === 0) {
                document.getElementById('matches-container').innerHTML = `
                    <div class="no-matches">
                        <div class="no-matches-icon">🤷‍♂️</div>
                        <h3>Матчи не найдены</h3>
                        <p>Попробуйте изменить параметры поиска или отключить фильтрацию</p>
                    </div>
                `;
                return;
            }
            
            // Group matches by league
            const matchesByLeague = {};
            data.matches.forEach(match => {
                if (!matchesByLeague[match.league_name]) {
                    matchesByLeague[match.league_name] = [];
                }
                matchesByLeague[match.league_name].push(match);
            });
            
            let html = `<h3>📋 Найдено ${data.total_matches} матчей из ${Object.keys(matchesByLeague).length} лиг</h3>`;
            
            // Display matches grouped by league
            Object.entries(matchesByLeague).forEach(([leagueName, matches]) => {
                html += `
                    <div style="margin-bottom: 30px;">
                        <h4 style="color: #667eea; margin-bottom: 15px; padding: 10px; background: #f0f2ff; border-radius: 5px;">
                            🏆 ${leagueName} (${matches.length} матчей)
                        </h4>
                `;
                
                matches.forEach(match => {
                    const matchTime = new Date(match.start_time).toLocaleString('ru-RU');
                    const statusClass = getStatusClass(match.status);
                    const statusText = getStatusText(match.status);
                    
                    html += `
                        <div class="match-card">
                            <div class="match-header">
                                <div class="teams">
                                    ${match.home_team.name} 
                                    <span class="vs">vs</span> 
                                    ${match.away_team.name}
                                </div>
                                <div class="match-status ${statusClass}">${statusText}</div>
                            </div>
                            
                            ${match.current_result ? `
                                <div class="score">⚽ ${match.current_result}</div>
                            ` : ''}
                            
                            <div class="match-details">
                                <div class="detail-item">
                                    <span>🕐</span>
                                    <span>${matchTime}</span>
                                </div>
                                <div class="detail-item">
                                    <span>🆔</span>
                                    <span>${match.event_id}</span>
                                </div>
                                <div class="detail-item">
                                    <span>🌍</span>
                                    <span>Страна ID: ${match.country_id}</span>
                                </div>
                                <div class="detail-item">
                                    <span>🔗</span>
                                    <a href="${match.match_url}" target="_blank">Открыть на FlashScore</a>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
            });
            
            document.getElementById('matches-container').innerHTML = html;
            document.getElementById('export-buttons').style.display = 'flex';
        }
        
        function updateStatistics(matches) {
            const stats = {
                total: matches.length,
                live: 0,
                finished: 0,
                scheduled: 0,
                leagues: new Set()
            };
            
            matches.forEach(match => {
                stats.leagues.add(match.league_name);
                
                if (match.status === 'FINISHED') {
                    stats.finished++;
                } else if (['FIRST_HALF', 'SECOND_HALF', 'HALF_TIME'].includes(match.status)) {
                    stats.live++;
                } else {
                    stats.scheduled++;
                }
            });
            
            document.getElementById('total-matches').textContent = stats.total;
            document.getElementById('live-matches').textContent = stats.live;
            document.getElementById('finished-matches').textContent = stats.finished;
            document.getElementById('scheduled-matches').textContent = stats.scheduled;
            document.getElementById('unique-leagues').textContent = stats.leagues.size;
            
            document.getElementById('stats-container').style.display = 'grid';
            currentStats = stats;
        }
        
        function getStatusClass(status) {
            if (status === 'FINISHED') return 'status-finished';
            if (['FIRST_HALF', 'SECOND_HALF', 'HALF_TIME'].includes(status)) return 'status-live';
            return 'status-scheduled';
        }
        
        function getStatusText(status) {
            const statusMap = {
                'FINISHED': 'Завершен',
                'FIRST_HALF': 'Первый тайм',
                'SECOND_HALF': 'Второй тайм',
                'HALF_TIME': 'Перерыв',
                '1': 'Запланирован'
            };
            return statusMap[status] || status;
        }
        
        async function fetchRealData() {
            const sportId = document.getElementById('sport-select').value;
            const dayOffset = document.getElementById('day-select').value;
            const useWhitelist = document.getElementById('whitelist-select').value;
            const whitelistMode = document.getElementById('mode-select').value;
            
            showLoading();
            
            try {
                const url = `/api/matches?sport=${sportId}&day=${dayOffset}&whitelist=${useWhitelist}&mode=${whitelistMode}`;
                const response = await fetch(url);
                const data = await response.json();
                
                displayMatches(data);
                
            } catch (error) {
                showError(`Ошибка сети: ${error.message}`);
            }
        }
        
        function exportJSON() {
            if (!currentData) {
                alert('Нет данных для экспорта');
                return;
            }
            
            const dataStr = JSON.stringify(currentData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `flashscore_matches_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
        
        function exportLegacy() {
            alert('Legacy экспорт будет добавлен в следующей версии');
        }
        
        async function showWhitelistStats() {
            try {
                const sportId = document.getElementById('sport-select').value;
                const response = await fetch(`/api/whitelist-stats?sport=${sportId}`);
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.stats;
                    alert(`Статистика фильтрации для спорта ${sportId}:
                    
Режим: ${stats.mode}
Включена: ${stats.enabled ? 'Да' : 'Нет'}
Стран в белом списке: ${stats.whitelisted_countries}
Лиг в белом списке: ${stats.whitelisted_leagues}
Паттернов: ${stats.whitelisted_patterns}

Примеры стран: ${stats.sample_countries.slice(0, 5).join(', ')}
Примеры лиг: ${stats.sample_leagues.slice(0, 3).join(', ')}`);
                }
            } catch (error) {
                alert('Ошибка получения статистики: ' + error.message);
            }
        }
        
        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(fetchRealData, 1000);
        });
    </script>
</body>
</html>
