#!/usr/bin/env python3
"""
Быстрый тест для проверки парсера с вашим whitelist
"""

from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import WhitelistMode
from whitelist import sp_good_ligs

def quick_test():
    print("🚀 БЫСТРЫЙ ТЕСТ ПАРСЕРА")
    print("="*50)
    
    parser = FlashScoreParser()
    
    # 1. Тест без фильтрации
    print("1️⃣ БЕЗ ФИЛЬТРАЦИИ:")
    parser.enable_whitelist(False)
    all_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=False)
    print(f"   Найдено: {len(all_matches)} матчей")
    
    if all_matches:
        print("   Примеры матчей:")
        for i, match in enumerate(all_matches[:3]):
            print(f"   {i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"      Лига: {match.league_name}")
            print(f"      Country ID: {match.country_id}")
    
    # 2. Тест с вашим оригинальным whitelist
    print(f"\n2️⃣ С ВАШИМ WHITELIST ({len(sp_good_ligs)} ID):")
    parser.enable_whitelist(True)
    parser.update_whitelist_mode(WhitelistMode.ORIGINAL_WHITELIST)
    filtered_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=True)
    print(f"   Найдено: {len(filtered_matches)} матчей")
    
    if filtered_matches:
        print("   Отфильтрованные матчи:")
        for i, match in enumerate(filtered_matches):
            print(f"   {i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"      Лига: {match.league_name}")
            print(f"      Country ID: {match.country_id}")
            print(f"      Время: {match.start_time.strftime('%H:%M')}")
            if match.current_result:
                print(f"      Счет: {match.current_result}")
    else:
        print("   ❌ Нет матчей по вашему whitelist")
    
    # 3. Статистика
    print(f"\n3️⃣ СТАТИСТИКА:")
    if len(all_matches) > 0:
        filter_rate = ((len(all_matches) - len(filtered_matches)) / len(all_matches)) * 100
        print(f"   Всего матчей: {len(all_matches)}")
        print(f"   После фильтрации: {len(filtered_matches)}")
        print(f"   Отфильтровано: {filter_rate:.1f}%")
    
    # 4. Анализ country_id
    all_country_ids = set(match.country_id for match in all_matches)
    whitelist_country_ids = all_country_ids.intersection(set(sp_good_ligs))
    
    print(f"\n4️⃣ АНАЛИЗ COUNTRY_ID:")
    print(f"   Все country_id: {sorted(all_country_ids)}")
    print(f"   В whitelist: {sorted(whitelist_country_ids)}")
    
    # 5. Сохранение результатов
    if filtered_matches:
        parser.save_matches_to_json(filtered_matches, "quick_test_filtered.json")
        parser.save_matches_to_legacy_format(filtered_matches, "quick_test_legacy.json")
        print(f"\n5️⃣ ФАЙЛЫ СОХРАНЕНЫ:")
        print(f"   quick_test_filtered.json - современный формат")
        print(f"   quick_test_legacy.json - формат оригинального парсера")
    
    print(f"\n✅ ТЕСТ ЗАВЕРШЕН!")
    return len(all_matches), len(filtered_matches)

if __name__ == "__main__":
    quick_test()
