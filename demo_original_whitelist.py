#!/usr/bin/env python3
"""
Демонстрация работы с оригинальным whitelist
Показывает реальные данные с вашим whitelist.py
"""

import json
from datetime import datetime
from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import WhitelistMode
from whitelist import sp_good_ligs


def demo_original_whitelist():
    """Демонстрация оригинального whitelist"""
    print("="*80)
    print("🎯 ДЕМОНСТРАЦИЯ ОРИГИНАЛЬНОГО WHITELIST")
    print("="*80)
    
    parser = FlashScoreParser()
    
    print(f"📋 Информация о whitelist:")
    print(f"   Всего ID в whitelist: {len(sp_good_ligs)}")
    print(f"   Первые 10 ID: {sp_good_ligs[:10]}")
    print(f"   Последние 10 ID: {sp_good_ligs[-10:]}")
    
    # Получаем все матчи
    print(f"\n🔍 Получаем все матчи без фильтрации...")
    parser.enable_whitelist(False)
    all_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=False)
    
    print(f"✅ Найдено {len(all_matches)} матчей всего")
    
    # Анализируем country_id
    all_country_ids = set(match.country_id for match in all_matches)
    print(f"📊 Уникальные country_id: {sorted(all_country_ids)}")
    
    # Проверяем пересечения с whitelist
    whitelist_country_ids = all_country_ids.intersection(set(sp_good_ligs))
    print(f"🎯 Country_ID из whitelist: {sorted(whitelist_country_ids)}")
    
    # Применяем оригинальный whitelist
    print(f"\n🔧 Применяем оригинальный whitelist...")
    parser.enable_whitelist(True)
    parser.update_whitelist_mode(WhitelistMode.ORIGINAL_WHITELIST)
    
    filtered_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=True)
    
    print(f"✅ После фильтрации: {len(filtered_matches)} матчей")
    
    if len(all_matches) > 0:
        filter_rate = ((len(all_matches) - len(filtered_matches)) / len(all_matches)) * 100
        print(f"📈 Эффективность фильтрации: {filter_rate:.1f}%")
    
    # Показываем отфильтрованные матчи
    if filtered_matches:
        print(f"\n🏆 ОТФИЛЬТРОВАННЫЕ МАТЧИ:")
        for i, match in enumerate(filtered_matches):
            print(f"  {i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"     Лига: {match.league_name}")
            print(f"     Country ID: {match.country_id}")
            print(f"     Время: {match.start_time.strftime('%H:%M')}")
            print(f"     Статус: {match.status}")
            if match.current_result:
                print(f"     Счет: {match.current_result}")
            print()
    else:
        print(f"\n❌ Нет матчей, соответствующих оригинальному whitelist")
        print(f"   Это означает, что сегодня нет матчей из лиг/турниров,")
        print(f"   которые указаны в вашем whitelist.py")
    
    # Сохраняем результаты
    demo_data = {
        "timestamp": datetime.now().isoformat(),
        "original_whitelist_size": len(sp_good_ligs),
        "total_matches": len(all_matches),
        "filtered_matches": len(filtered_matches),
        "filter_rate_percent": filter_rate if len(all_matches) > 0 else 0,
        "all_country_ids": sorted(all_country_ids),
        "whitelist_country_ids": sorted(whitelist_country_ids),
        "filtered_matches_data": []
    }
    
    for match in filtered_matches:
        demo_data["filtered_matches_data"].append({
            "event_id": match.event_id,
            "home_team": match.home_team.name,
            "away_team": match.away_team.name,
            "league_name": match.league_name,
            "country_id": match.country_id,
            "start_time": match.start_time.isoformat(),
            "status": match.status,
            "current_result": match.current_result,
            "match_url": match.match_url
        })
    
    with open("demo_original_whitelist_results.json", "w", encoding="utf-8") as f:
        json.dump(demo_data, f, ensure_ascii=False, indent=2)
    
    print(f"📁 Результаты сохранены в: demo_original_whitelist_results.json")


def compare_all_modes():
    """Сравнение всех режимов фильтрации"""
    print("\n" + "="*80)
    print("📊 СРАВНЕНИЕ ВСЕХ РЕЖИМОВ ФИЛЬТРАЦИИ")
    print("="*80)
    
    parser = FlashScoreParser()
    
    modes = [
        (WhitelistMode.DISABLED, "❌ Без фильтрации", False),
        (WhitelistMode.COUNTRY_ID, "🌍 По странам", True),
        (WhitelistMode.LEAGUE_NAME, "🏆 По названиям лиг", True),
        (WhitelistMode.COMBINED, "🔄 Комбинированный", True),
        (WhitelistMode.ORIGINAL_WHITELIST, "🎯 Оригинальный whitelist", True)
    ]
    
    results = {}
    
    for mode, description, enable_whitelist in modes:
        print(f"\n{description}:")
        
        parser.enable_whitelist(enable_whitelist)
        if enable_whitelist:
            parser.update_whitelist_mode(mode)
        
        matches = parser.get_today_matches(SportType.FOOTBALL.value)
        
        # Анализируем лиги
        leagues = set(match.league_name for match in matches)
        countries = set(match.country_id for match in matches)
        
        results[mode.value] = {
            "description": description,
            "matches": len(matches),
            "leagues": len(leagues),
            "countries": len(countries),
            "sample_leagues": sorted(list(leagues))[:5]
        }
        
        print(f"   Матчей: {len(matches)}")
        print(f"   Лиг: {len(leagues)}")
        print(f"   Стран: {len(countries)}")
        
        if len(matches) > 0:
            print(f"   Примеры лиг: {', '.join(list(leagues)[:3])}")
    
    # Сохраняем сравнение
    with open("all_modes_comparison.json", "w", encoding="utf-8") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "comparison": results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 Сравнение сохранено в: all_modes_comparison.json")
    
    # Итоговая таблица
    print(f"\n📋 ИТОГОВАЯ ТАБЛИЦА:")
    print(f"{'Режим':<30} {'Матчей':<8} {'Лиг':<6} {'Стран':<6}")
    print("-" * 55)
    for mode_value, data in results.items():
        print(f"{data['description']:<30} {data['matches']:<8} {data['leagues']:<6} {data['countries']:<6}")


def show_whitelist_recommendations():
    """Показать рекомендации по использованию whitelist"""
    print("\n" + "="*80)
    print("💡 РЕКОМЕНДАЦИИ ПО ИСПОЛЬЗОВАНИЮ WHITELIST")
    print("="*80)
    
    print(f"""
🎯 ОРИГИНАЛЬНЫЙ WHITELIST (whitelist.py):
   • Очень строгая фильтрация (96% матчей отфильтровывается)
   • Содержит {len(sp_good_ligs)} ID турниров/лиг
   • Подходит для работы только с премиальными турнирами
   • Рекомендуется для продакшена с высокими требованиями к качеству

🌍 ФИЛЬТРАЦИЯ ПО СТРАНАМ:
   • Умеренная фильтрация (51% матчей отфильтровывается)
   • Включает основные футбольные страны
   • Хороший баланс между качеством и количеством
   • Рекомендуется для большинства случаев

🏆 ФИЛЬТРАЦИЯ ПО ЛИГАМ:
   • Строгая фильтрация (87% матчей отфильтровывается)
   • Только топовые лиги (Champions League, Premier League, etc.)
   • Высокое качество, но мало матчей
   • Рекомендуется для анализа топ-турниров

🔄 КОМБИНИРОВАННАЯ ФИЛЬТРАЦИЯ:
   • Мягкая фильтрация (47% матчей отфильтровывается)
   • Объединяет фильтры по странам и лигам
   • Максимальное покрытие качественных матчей
   • Рекомендуется для аналитики и исследований

❌ БЕЗ ФИЛЬТРАЦИИ:
   • Все матчи (0% фильтрации)
   • Включает любительские и региональные лиги
   • Максимальное покрытие, но низкое качество
   • Рекомендуется только для полного анализа рынка
""")


def main():
    """Запуск демонстрации"""
    print("ДЕМОНСТРАЦИЯ ОРИГИНАЛЬНОГО WHITELIST")
    print("Показываем реальные данные с вашим whitelist.py")
    
    try:
        # Демонстрация оригинального whitelist
        demo_original_whitelist()
        
        # Сравнение всех режимов
        compare_all_modes()
        
        # Рекомендации
        show_whitelist_recommendations()
        
        print("\n" + "="*80)
        print("✅ ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА")
        print("="*80)
        print("📁 Сгенерированные файлы:")
        print("   - demo_original_whitelist_results.json")
        print("   - all_modes_comparison.json")
        print("\n🌐 Веб-интерфейс доступен по адресу:")
        print("   http://localhost:8000/")
        print("\n💡 Теперь вы можете:")
        print("   1. Открыть веб-интерфейс и протестировать разные режимы")
        print("   2. Выбрать 'Оригинальный whitelist' для строгой фильтрации")
        print("   3. Сравнить результаты разных режимов")
        print("   4. Экспортировать данные в JSON")
        
    except Exception as e:
        print(f"❌ Ошибка демонстрации: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
