#!/usr/bin/env python3
"""
Analyze the original whitelist to understand what the IDs represent
"""

import json
from datetime import datetime
from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import WhitelistMode, ORIGINAL_WHITELIST_IDS
from whitelist import sp_good_ligs


def analyze_whitelist_ids():
    """Analyze what the original whitelist IDs represent"""
    print("="*70)
    print("АНАЛИЗ ОРИГИНАЛЬНОГО WHITELIST")
    print("="*70)
    
    print(f"Всего ID в whitelist: {len(sp_good_ligs)}")
    print(f"Минимальный ID: {min(sp_good_ligs)}")
    print(f"Максимальный ID: {max(sp_good_ligs)}")
    print(f"Первые 10 ID: {sp_good_ligs[:10]}")
    print(f"Последние 10 ID: {sp_good_ligs[-10:]}")
    
    # Анализ диапазонов
    ranges = {
        "1-100": 0,
        "101-1000": 0,
        "1001-5000": 0,
        "5001-10000": 0,
        "10000+": 0
    }
    
    for id_val in sp_good_ligs:
        if id_val <= 100:
            ranges["1-100"] += 1
        elif id_val <= 1000:
            ranges["101-1000"] += 1
        elif id_val <= 5000:
            ranges["1001-5000"] += 1
        elif id_val <= 10000:
            ranges["5001-10000"] += 1
        else:
            ranges["10000+"] += 1
    
    print(f"\nРаспределение по диапазонам:")
    for range_name, count in ranges.items():
        print(f"  {range_name}: {count} ID")


def test_original_whitelist_mode():
    """Test the original whitelist mode"""
    print("\n" + "="*70)
    print("ТЕСТ ОРИГИНАЛЬНОГО РЕЖИМА ФИЛЬТРАЦИИ")
    print("="*70)
    
    parser = FlashScoreParser()
    
    # Включаем оригинальный режим фильтрации
    parser.update_whitelist_mode(WhitelistMode.ORIGINAL_WHITELIST)
    
    # Получаем все матчи без фильтрации
    parser.enable_whitelist(False)
    all_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=False)
    
    # Получаем матчи с оригинальной фильтрацией
    parser.enable_whitelist(True)
    filtered_matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=True)
    
    print(f"Всего матчей: {len(all_matches)}")
    print(f"После оригинальной фильтрации: {len(filtered_matches)}")
    print(f"Отфильтровано: {len(all_matches) - len(filtered_matches)}")
    
    if len(all_matches) > 0:
        filter_rate = ((len(all_matches) - len(filtered_matches)) / len(all_matches)) * 100
        print(f"Процент фильтрации: {filter_rate:.1f}%")
    
    # Анализ country_id в матчах
    country_ids_all = set(match.country_id for match in all_matches)
    country_ids_filtered = set(match.country_id for match in filtered_matches)
    
    print(f"\nВсе country_id в матчах: {sorted(country_ids_all)}")
    print(f"Country_id после фильтрации: {sorted(country_ids_filtered)}")
    
    # Проверяем, какие country_id есть в оригинальном whitelist
    whitelist_country_ids = country_ids_all.intersection(ORIGINAL_WHITELIST_IDS)
    print(f"Country_ID, которые есть в оригинальном whitelist: {sorted(whitelist_country_ids)}")
    
    return all_matches, filtered_matches


def analyze_match_data_structure():
    """Analyze the structure of match data to understand IDs"""
    print("\n" + "="*70)
    print("АНАЛИЗ СТРУКТУРЫ ДАННЫХ МАТЧЕЙ")
    print("="*70)
    
    parser = FlashScoreParser()
    parser.enable_whitelist(False)
    matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=False)
    
    if not matches:
        print("Нет матчей для анализа")
        return
    
    print(f"Анализируем {len(matches)} матчей...")
    
    # Собираем уникальные значения
    country_ids = set()
    league_names = set()
    event_ids = set()
    
    for match in matches:
        country_ids.add(match.country_id)
        league_names.add(match.league_name)
        event_ids.add(match.event_id)
    
    print(f"\nУникальные country_id: {len(country_ids)}")
    print(f"Примеры country_id: {sorted(list(country_ids))[:10]}")
    
    print(f"\nУникальные лиги: {len(league_names)}")
    print(f"Примеры лиг:")
    for i, league in enumerate(sorted(list(league_names))[:10]):
        print(f"  {i+1}. {league}")
    
    print(f"\nУникальные event_id: {len(event_ids)}")
    print(f"Примеры event_id: {sorted(list(event_ids))[:10]}")
    
    # Проверяем, есть ли пересечения с оригинальным whitelist
    country_in_whitelist = country_ids.intersection(ORIGINAL_WHITELIST_IDS)
    print(f"\nCountry_ID, которые есть в оригинальном whitelist: {sorted(country_in_whitelist)}")
    
    # Сохраняем детальный анализ
    analysis_data = {
        "timestamp": datetime.now().isoformat(),
        "total_matches": len(matches),
        "unique_country_ids": sorted(list(country_ids)),
        "unique_league_names": sorted(list(league_names)),
        "unique_event_ids": sorted(list(event_ids)),
        "original_whitelist_size": len(ORIGINAL_WHITELIST_IDS),
        "country_ids_in_whitelist": sorted(country_in_whitelist),
        "sample_matches": []
    }
    
    # Добавляем примеры матчей
    for match in matches[:5]:
        analysis_data["sample_matches"].append({
            "event_id": match.event_id,
            "country_id": match.country_id,
            "league_name": match.league_name,
            "home_team": match.home_team.name,
            "away_team": match.away_team.name,
            "in_original_whitelist": match.country_id in ORIGINAL_WHITELIST_IDS
        })
    
    with open("original_whitelist_analysis.json", "w", encoding="utf-8") as f:
        json.dump(analysis_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 Детальный анализ сохранен в: original_whitelist_analysis.json")


def compare_filtering_modes():
    """Compare different filtering modes"""
    print("\n" + "="*70)
    print("СРАВНЕНИЕ РЕЖИМОВ ФИЛЬТРАЦИИ")
    print("="*70)
    
    parser = FlashScoreParser()
    
    modes_to_test = [
        (WhitelistMode.DISABLED, "Без фильтрации"),
        (WhitelistMode.COUNTRY_ID, "По country_id"),
        (WhitelistMode.LEAGUE_NAME, "По названиям лиг"),
        (WhitelistMode.COMBINED, "Комбинированный"),
        (WhitelistMode.ORIGINAL_WHITELIST, "Оригинальный whitelist")
    ]
    
    results = {}
    
    for mode, description in modes_to_test:
        print(f"\nТестируем режим: {description}")
        
        if mode == WhitelistMode.DISABLED:
            parser.enable_whitelist(False)
        else:
            parser.enable_whitelist(True)
            parser.update_whitelist_mode(mode)
        
        matches = parser.get_today_matches(SportType.FOOTBALL.value)
        
        results[mode.value] = {
            "description": description,
            "match_count": len(matches),
            "unique_leagues": len(set(match.league_name for match in matches)),
            "unique_countries": len(set(match.country_id for match in matches))
        }
        
        print(f"  Матчей: {len(matches)}")
        print(f"  Уникальных лиг: {results[mode.value]['unique_leagues']}")
        print(f"  Уникальных стран: {results[mode.value]['unique_countries']}")
    
    # Сохраняем сравнение
    with open("filtering_modes_comparison.json", "w", encoding="utf-8") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "comparison": results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 Сравнение режимов сохранено в: filtering_modes_comparison.json")
    
    # Выводим итоговую таблицу
    print(f"\n📊 ИТОГОВАЯ ТАБЛИЦА:")
    print(f"{'Режим':<25} {'Матчей':<8} {'Лиг':<6} {'Стран':<6}")
    print("-" * 50)
    for mode_value, data in results.items():
        print(f"{data['description']:<25} {data['match_count']:<8} {data['unique_leagues']:<6} {data['unique_countries']:<6}")


def main():
    """Запуск всех анализов"""
    print("АНАЛИЗ ОРИГИНАЛЬНОГО WHITELIST И РЕЖИМОВ ФИЛЬТРАЦИИ")
    print("Цель: понять, что представляют ID в whitelist.py и как их использовать")
    
    try:
        # Анализ самого whitelist
        analyze_whitelist_ids()
        
        # Анализ структуры данных матчей
        analyze_match_data_structure()
        
        # Тест оригинального режима
        test_original_whitelist_mode()
        
        # Сравнение всех режимов
        compare_filtering_modes()
        
        print("\n" + "="*70)
        print("АНАЛИЗ ЗАВЕРШЕН")
        print("="*70)
        print("✅ Все анализы выполнены успешно!")
        print("\nСгенерированные файлы:")
        print("  - original_whitelist_analysis.json")
        print("  - filtering_modes_comparison.json")
        print("\nВыводы:")
        print("  1. Проанализированы ID в оригинальном whitelist")
        print("  2. Проверена структура данных матчей")
        print("  3. Протестированы все режимы фильтрации")
        print("  4. Создано сравнение эффективности фильтров")
        
    except Exception as e:
        print(f"❌ Ошибка анализа: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
