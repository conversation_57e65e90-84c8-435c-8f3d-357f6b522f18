<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Простой тест парсера</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #667eea;
            margin-top: 0;
        }
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .match {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        .match-teams {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }
        .match-details {
            color: #666;
            margin-top: 5px;
        }
        .score {
            color: #28a745;
            font-weight: bold;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Простой тест FlashScore парсера</h1>
        
        <div class="test-section">
            <h2>🎯 Тест с вашим whitelist</h2>
            <p>Нажмите кнопку, чтобы получить реальные данные с применением вашего whitelist.py</p>
            <button onclick="testWithWhitelist()">Тест с whitelist</button>
            <button onclick="testWithoutWhitelist()">Тест без whitelist</button>
            <button onclick="compareResults()">Сравнить результаты</button>
            
            <div id="results"></div>
        </div>
    </div>

    <script>
        let allMatches = null;
        let filteredMatches = null;

        async function testWithWhitelist() {
            document.getElementById('results').innerHTML = '<div class="loading">Загрузка с whitelist...</div>';
            
            try {
                const response = await fetch('/api/matches?sport=1&day=0&whitelist=true&mode=original_whitelist');
                const data = await response.json();
                
                if (data.success) {
                    filteredMatches = data;
                    displayResults('whitelist', data);
                } else {
                    showError('Ошибка: ' + data.error);
                }
            } catch (error) {
                showError('Ошибка сети: ' + error.message);
            }
        }

        async function testWithoutWhitelist() {
            document.getElementById('results').innerHTML = '<div class="loading">Загрузка без whitelist...</div>';
            
            try {
                const response = await fetch('/api/matches?sport=1&day=0&whitelist=false');
                const data = await response.json();
                
                if (data.success) {
                    allMatches = data;
                    displayResults('no_whitelist', data);
                } else {
                    showError('Ошибка: ' + data.error);
                }
            } catch (error) {
                showError('Ошибка сети: ' + error.message);
            }
        }

        async function compareResults() {
            if (!allMatches || !filteredMatches) {
                alert('Сначала выполните оба теста');
                return;
            }

            const filterRate = ((allMatches.total_matches - filteredMatches.total_matches) / allMatches.total_matches * 100).toFixed(1);
            
            let html = `
                <div class="stats">
                    <h3>📊 Сравнение результатов</h3>
                    <p><strong>Всего матчей:</strong> ${allMatches.total_matches}</p>
                    <p><strong>После whitelist:</strong> ${filteredMatches.total_matches}</p>
                    <p><strong>Отфильтровано:</strong> ${filterRate}%</p>
                </div>
            `;

            if (filteredMatches.matches.length > 0) {
                html += '<h3>🏆 Матчи после фильтрации:</h3>';
                filteredMatches.matches.forEach((match, index) => {
                    const time = new Date(match.start_time).toLocaleTimeString('ru-RU', {hour: '2-digit', minute: '2-digit'});
                    html += `
                        <div class="match">
                            <div class="match-teams">${match.home_team.name} vs ${match.away_team.name}</div>
                            <div class="match-details">
                                🏆 ${match.league_name} | 🕐 ${time} | 🌍 Country ID: ${match.country_id}
                                ${match.current_result ? `| <span class="score">⚽ ${match.current_result}</span>` : ''}
                            </div>
                        </div>
                    `;
                });
            } else {
                html += '<p>❌ Нет матчей после фильтрации</p>';
            }

            document.getElementById('results').innerHTML = html;
        }

        function displayResults(type, data) {
            const title = type === 'whitelist' ? '🎯 Результаты с whitelist' : '📋 Результаты без whitelist';
            
            let html = `
                <div class="stats">
                    <h3>${title}</h3>
                    <p><strong>Найдено матчей:</strong> ${data.total_matches}</p>
                    <p><strong>Режим фильтрации:</strong> ${data.whitelist_mode || 'отключен'}</p>
                    <p><strong>Время загрузки:</strong> ${new Date(data.timestamp).toLocaleTimeString('ru-RU')}</p>
                </div>
            `;

            if (data.matches.length > 0) {
                html += `<h3>Примеры матчей (первые ${Math.min(5, data.matches.length)}):</h3>`;
                data.matches.slice(0, 5).forEach((match, index) => {
                    const time = new Date(match.start_time).toLocaleTimeString('ru-RU', {hour: '2-digit', minute: '2-digit'});
                    html += `
                        <div class="match">
                            <div class="match-teams">${match.home_team.name} vs ${match.away_team.name}</div>
                            <div class="match-details">
                                🏆 ${match.league_name} | 🕐 ${time} | 🌍 Country ID: ${match.country_id}
                                ${match.current_result ? `| <span class="score">⚽ ${match.current_result}</span>` : ''}
                            </div>
                        </div>
                    `;
                });

                if (data.matches.length > 5) {
                    html += `<p>... и еще ${data.matches.length - 5} матчей</p>`;
                }
            } else {
                html += '<p>❌ Матчи не найдены</p>';
            }

            document.getElementById('results').innerHTML = html;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <h3>❌ Ошибка</h3>
                    <p>${message}</p>
                    <p>Убедитесь, что веб-сервер запущен: <code>python3 web_server.py</code></p>
                </div>
            `;
        }

        // Автоматически загружаем данные при открытии страницы
        window.addEventListener('load', () => {
            setTimeout(() => {
                testWithWhitelist();
            }, 1000);
        });
    </script>
</body>
</html>
